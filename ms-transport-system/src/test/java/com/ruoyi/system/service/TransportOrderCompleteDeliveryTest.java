package com.ruoyi.system.service;

import com.ruoyi.system.domain.transport.TransportOrder;
import com.ruoyi.system.domain.transport.TransportReceipts;
import com.ruoyi.system.service.transport.ITransportOrderService;
import com.ruoyi.system.service.transport.ITransportOrderStatusService;
import com.ruoyi.system.service.transport.ITransportReceiptsService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 运输单完成配送自动生成对账单测试类
 *
 * <AUTHOR>
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class TransportOrderCompleteDeliveryTest {

    @Autowired
    private ITransportOrderService transportOrderService;

    @Autowired
    private ITransportOrderStatusService transportOrderStatusService;

    @Autowired
    private ITransportReceiptsService transportReceiptsService;

    @Test
    public void testCompleteDeliveryGeneratesReceipts() {
        // 1. 创建测试运输单
        TransportOrder order = createTestTransportOrder();
        int insertResult = transportOrderService.insertTransportOrder(order);
        assertEquals(1, insertResult);
        assertNotNull(order.getId());

        // 2. 模拟运输单状态流转到运输中
        transportOrderStatusService.updateOrderStatus(order.getId(), 5); // 运输中

        // 3. 完成配送
        int completeResult = transportOrderStatusService.completeDelivery(order.getId());
        assertEquals(1, completeResult);

        // 4. 验证运输单状态已更新为已送达
        TransportOrder updatedOrder = transportOrderService.selectTransportOrderById(order.getId());
        assertEquals(6, updatedOrder.getOrderStatus().intValue()); // 已送达

        // 5. 验证对账单已自动生成
        String expectedReceiptNumber = generateExpectedReceiptNumber(order);
        TransportReceipts receipts = transportReceiptsService.findByReceiptNumber(expectedReceiptNumber);
        
        assertNotNull(receipts, "对账单应该已自动生成");
        assertEquals(expectedReceiptNumber, receipts.getReceiptNumber());
        assertEquals(order.getShippingCost(), receipts.getAmount());
        assertEquals(1, receipts.getFundType().intValue()); // 应付款
        assertEquals("TEST_CUST", receipts.getCustomerCode());
        assertEquals(order.getCustomerName(), receipts.getCustomerName());
        assertEquals(order.getInternalCode(), receipts.getInternalCode());
        assertTrue(receipts.getDescription().contains(order.getOrderNo()));
    }

    @Test
    public void testCompleteDeliveryWithExistingReceipts() {
        // 1. 创建测试运输单
        TransportOrder order = createTestTransportOrder();
        transportOrderService.insertTransportOrder(order);

        // 2. 预先创建同样收据号码的对账单
        String receiptNumber = generateExpectedReceiptNumber(order);
        TransportReceipts existingReceipts = new TransportReceipts();
        existingReceipts.setReceiptNumber(receiptNumber);
        existingReceipts.setAmount(new BigDecimal("1000.00"));
        existingReceipts.setFundType(0);
        existingReceipts.setDescription("预先存在的对账单");
        existingReceipts.setCustomerCode("TEST_CUST");
        existingReceipts.setCustomerName("测试客户");
        existingReceipts.setUsedAmount(BigDecimal.ZERO);
        existingReceipts.setTotalExpenseAmount(BigDecimal.ZERO);
        existingReceipts.setTotalArrearsAmount(BigDecimal.ZERO);
        existingReceipts.setCreateBy("test");
        transportReceiptsService.insert(existingReceipts);

        // 3. 完成配送
        int completeResult = transportOrderStatusService.completeDelivery(order.getId());
        assertEquals(1, completeResult); // 状态更新应该成功

        // 4. 验证不会重复生成对账单
        List<TransportReceipts> receiptsList = transportReceiptsService.findByCustomerCode("TEST_CUST");
        long count = receiptsList.stream()
                .filter(r -> receiptNumber.equals(r.getReceiptNumber()))
                .count();
        assertEquals(1, count, "不应该重复生成相同收据号码的对账单");
    }

    @Test
    public void testCompleteDeliveryWithNullShippingCost() {
        // 1. 创建运费为空的测试运输单
        TransportOrder order = createTestTransportOrder();
        order.setShippingCost(null);
        transportOrderService.insertTransportOrder(order);

        // 2. 完成配送
        int completeResult = transportOrderStatusService.completeDelivery(order.getId());
        assertEquals(1, completeResult);

        // 3. 验证对账单生成，运费为0
        String expectedReceiptNumber = generateExpectedReceiptNumber(order);
        TransportReceipts receipts = transportReceiptsService.findByReceiptNumber(expectedReceiptNumber);
        
        assertNotNull(receipts);
        assertEquals(BigDecimal.ZERO, receipts.getTotalExpenseAmount());
    }

    /**
     * 创建测试运输单
     */
    private TransportOrder createTestTransportOrder() {
        TransportOrder order = new TransportOrder();
        order.setOrderNo("TEST" + System.currentTimeMillis());
        order.setInternalCode("INT" + System.currentTimeMillis());
        order.setCustomerId(1L);
        order.setCustomerName("测试客户");
        order.setConsigneeId(1L);
        order.setConsigneeName("测试收货方");
        order.setConsigneeAddress("测试收货地址");
        order.setConsigneeContact("测试联系人");
        order.setConsigneePhone("13800138000");
        order.setLoadingPointId(1L);
        order.setLoadingPointName("测试装货点");
        order.setLoadingAddress("测试装货地址");
        order.setProductName("测试油品");
        order.setProductQuantity(new BigDecimal("10.00"));
        order.setTotalVolume(new BigDecimal("12000.00"));
        order.setTransportDistance(new BigDecimal("100.00"));
        order.setOrderStatus(1); // 待指派
        order.setShippingCost(new BigDecimal("5000.00"));
        order.setOtherExpenses(new BigDecimal("200.00"));
        order.setTaxIncluded(new BigDecimal("5.50"));
        order.setDeliveryRequirements("测试配送要求");
        order.setSpecialInstructions("测试特殊说明");
        order.setCreateBy("test");
        return order;
    }

    /**
     * 生成预期的收据号码
     */
    private String generateExpectedReceiptNumber(TransportOrder order) {
        java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyyMMdd");
        String dateStr = sdf.format(new Date());
        String sequence = String.format("%04d", order.getId() % 10000);
        return "TR" + dateStr + sequence;
    }
}
