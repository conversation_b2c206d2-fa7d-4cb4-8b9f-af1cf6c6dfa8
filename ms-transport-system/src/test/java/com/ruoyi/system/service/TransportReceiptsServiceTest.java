package com.ruoyi.system.service;

import com.ruoyi.system.domain.transport.TransportReceipts;
import com.ruoyi.system.service.transport.ITransportReceiptsService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 运输对账服务测试类
 *
 * <AUTHOR>
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class TransportReceiptsServiceTest {

    @Autowired
    private ITransportReceiptsService transportReceiptsService;

    @Test
    public void testInsertTransportReceipts() {
        // 创建测试数据
        TransportReceipts receipts = new TransportReceipts();
        receipts.setReceiptNumber("TEST001");
        receipts.setAmount(new BigDecimal("10000.00"));
        receipts.setFundType(0); // 预收款
        receipts.setDescription("测试预收款");
        receipts.setCustomerCode("TEST_CUST");
        receipts.setInternalCode("TEST_INT");
        receipts.setCustomerName("测试客户");
        receipts.setPaymentMethod("银行转账");
        receipts.setBankName("测试银行");
        receipts.setReceiptDate(new Date());
        receipts.setUsedAmount(new BigDecimal("10000.00"));
        receipts.setTotalPrepaidAmount(new BigDecimal("10000.00"));
        receipts.setTotalExpenseAmount(BigDecimal.ZERO);
        receipts.setTotalArrearsAmount(BigDecimal.ZERO);
        receipts.setCreateBy("test");

        // 执行插入
        int result = transportReceiptsService.insertTransportReceipts(receipts);
        
        // 验证结果
        assertEquals(1, result);
        assertNotNull(receipts.getId());
    }

    @Test
    public void testSelectTransportReceiptsById() {
        // 先插入测试数据
        TransportReceipts receipts = createTestReceipts("TEST002");
        transportReceiptsService.insertTransportReceipts(receipts);
        
        // 查询数据
        TransportReceipts found = transportReceiptsService.selectTransportReceiptsById(receipts.getId());
        
        // 验证结果
        assertNotNull(found);
        assertEquals("TEST002", found.getReceiptNumber());
        assertEquals("测试客户", found.getCustomerName());
    }

    @Test
    public void testSelectTransportReceiptsList() {
        // 插入测试数据
        TransportReceipts receipts1 = createTestReceipts("TEST003");
        TransportReceipts receipts2 = createTestReceipts("TEST004");
        transportReceiptsService.insertTransportReceipts(receipts1);
        transportReceiptsService.insertTransportReceipts(receipts2);
        
        // 查询列表
        TransportReceipts condition = new TransportReceipts();
        condition.setCustomerCode("TEST_CUST");
        List<TransportReceipts> list = transportReceiptsService.selectTransportReceiptsList(condition);
        
        // 验证结果
        assertNotNull(list);
        assertTrue(list.size() >= 2);
    }

    @Test
    public void testUpdateTransportReceipts() {
        // 先插入测试数据
        TransportReceipts receipts = createTestReceipts("TEST005");
        transportReceiptsService.insertTransportReceipts(receipts);
        
        // 修改数据
        receipts.setDescription("修改后的描述");
        receipts.setAmount(new BigDecimal("20000.00"));
        receipts.setUpdateBy("test_update");
        
        // 执行更新
        int result = transportReceiptsService.updateTransportReceipts(receipts);
        
        // 验证结果
        assertEquals(1, result);
        
        // 查询验证
        TransportReceipts updated = transportReceiptsService.selectTransportReceiptsById(receipts.getId());
        assertEquals("修改后的描述", updated.getDescription());
        assertEquals(new BigDecimal("20000.00"), updated.getAmount());
    }

    @Test
    public void testDeleteTransportReceipts() {
        // 先插入测试数据
        TransportReceipts receipts = createTestReceipts("TEST006");
        transportReceiptsService.insertTransportReceipts(receipts);
        
        // 执行删除
        int result = transportReceiptsService.deleteTransportReceiptsById(receipts.getId());
        
        // 验证结果
        assertEquals(1, result);
        
        // 查询验证（应该查不到，因为是逻辑删除）
        TransportReceipts deleted = transportReceiptsService.selectTransportReceiptsById(receipts.getId());
        assertNull(deleted);
    }

    @Test
    public void testFindByReceiptNumber() {
        // 先插入测试数据
        TransportReceipts receipts = createTestReceipts("TEST007");
        transportReceiptsService.insertTransportReceipts(receipts);
        
        // 根据收据号码查询
        TransportReceipts found = transportReceiptsService.findByReceiptNumber("TEST007");
        
        // 验证结果
        assertNotNull(found);
        assertEquals("TEST007", found.getReceiptNumber());
    }

    @Test
    public void testFindByCustomerCode() {
        // 插入测试数据
        TransportReceipts receipts1 = createTestReceipts("TEST008");
        TransportReceipts receipts2 = createTestReceipts("TEST009");
        transportReceiptsService.insertTransportReceipts(receipts1);
        transportReceiptsService.insertTransportReceipts(receipts2);
        
        // 根据客户编号查询
        List<TransportReceipts> list = transportReceiptsService.findByCustomerCode("TEST_CUST");
        
        // 验证结果
        assertNotNull(list);
        assertTrue(list.size() >= 2);
    }

    @Test
    public void testGetInitialAmount() {
        // 插入测试数据
        TransportReceipts receipts1 = createTestReceipts("TEST010");
        receipts1.setFundType(0); // 预收款
        receipts1.setAmount(new BigDecimal("10000.00"));
        
        TransportReceipts receipts2 = createTestReceipts("TEST011");
        receipts2.setFundType(1); // 应付款
        receipts2.setAmount(new BigDecimal("5000.00"));
        
        transportReceiptsService.insertTransportReceipts(receipts1);
        transportReceiptsService.insertTransportReceipts(receipts2);
        
        // 查询期初余额
        TransportReceipts condition = new TransportReceipts();
        condition.setCustomerCode("TEST_CUST");
        BigDecimal initialAmount = transportReceiptsService.getInitialAmount(condition);
        
        // 验证结果（预收款10000 - 应付款5000 = 5000）
        assertNotNull(initialAmount);
        assertTrue(initialAmount.compareTo(BigDecimal.ZERO) > 0);
    }

    /**
     * 创建测试对账数据
     */
    private TransportReceipts createTestReceipts(String receiptNumber) {
        TransportReceipts receipts = new TransportReceipts();
        receipts.setReceiptNumber(receiptNumber);
        receipts.setAmount(new BigDecimal("10000.00"));
        receipts.setFundType(0); // 预收款
        receipts.setDescription("测试数据");
        receipts.setCustomerCode("TEST_CUST");
        receipts.setInternalCode("TEST_INT");
        receipts.setCustomerName("测试客户");
        receipts.setPaymentMethod("银行转账");
        receipts.setBankName("测试银行");
        receipts.setReceiptDate(new Date());
        receipts.setUsedAmount(new BigDecimal("10000.00"));
        receipts.setTotalPrepaidAmount(new BigDecimal("10000.00"));
        receipts.setTotalExpenseAmount(BigDecimal.ZERO);
        receipts.setTotalArrearsAmount(BigDecimal.ZERO);
        receipts.setCreateBy("test");
        return receipts;
    }
}
