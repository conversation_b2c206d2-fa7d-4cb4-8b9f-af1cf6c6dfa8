package com.ruoyi.system.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 运输客户对账汇总VO
 *
 * <AUTHOR>
 */
@ApiModel("运输客户对账汇总")
@Data
public class TransportCustomerReceiptsSummaryVO {

    /** 客户编号 */
    @ApiModelProperty("客户编号")
    private String customerCode;

    /** 客户名称 */
    @ApiModelProperty("客户名称")
    private String customerName;

    /** 总预收金额 */
    @ApiModelProperty("总预收金额")
    private BigDecimal totalPrepaidAmount;

    /** 总支出金额 */
    @ApiModelProperty("总支出金额")
    private BigDecimal totalExpenseAmount;

    /** 总欠款金额 */
    @ApiModelProperty("总欠款金额")
    private BigDecimal totalArrearsAmount;

    /** 可用金额 */
    @ApiModelProperty("可用金额")
    private BigDecimal usedAmount;

    /** 余额 */
    @ApiModelProperty("余额")
    private BigDecimal balance;

    /**
     * 计算余额
     * 余额 = 总预收金额 - 总支出金额 + 总欠款金额
     */
    public BigDecimal getBalance() {
        BigDecimal prepaid = totalPrepaidAmount != null ? totalPrepaidAmount : BigDecimal.ZERO;
        BigDecimal expense = totalExpenseAmount != null ? totalExpenseAmount : BigDecimal.ZERO;
        BigDecimal arrears = totalArrearsAmount != null ? totalArrearsAmount : BigDecimal.ZERO;
        
        return prepaid.subtract(expense).add(arrears);
    }
}
