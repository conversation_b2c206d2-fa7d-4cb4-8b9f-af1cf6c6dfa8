package com.ruoyi.system.service.transport;

import com.ruoyi.system.domain.dto.TransportReceiptsDetails;
import com.ruoyi.system.domain.transport.TransportReceipts;

import java.math.BigDecimal;
import java.util.List;

/**
 * 运输对账Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public interface ITransportReceiptsService {
    
    /**
     * 查询运输对账
     * 
     * @param id 运输对账主键
     * @return 运输对账
     */
    public TransportReceipts selectTransportReceiptsById(Long id);

    /**
     * 查询运输对账列表
     * 
     * @param transportReceipts 运输对账
     * @return 运输对账集合
     */
    public List<TransportReceipts> selectTransportReceiptsList(TransportReceipts transportReceipts);

    /**
     * 新增运输对账
     * 
     * @param transportReceipts 运输对账
     * @return 结果
     */
    public int insertTransportReceipts(TransportReceipts transportReceipts);

    /**
     * 修改运输对账
     * 
     * @param transportReceipts 运输对账
     * @return 结果
     */
    public int updateTransportReceipts(TransportReceipts transportReceipts);

    /**
     * 批量删除运输对账
     * 
     * @param ids 需要删除的运输对账主键集合
     * @return 结果
     */
    public int deleteTransportReceiptsByIds(Long[] ids);

    /**
     * 删除运输对账信息
     * 
     * @param id 运输对账主键
     * @return 结果
     */
    public int deleteTransportReceiptsById(Long id);

    /**
     * 根据收据号码查询运输对账
     * 
     * @param receiptNumber 收据号码
     * @return 运输对账
     */
    public TransportReceipts findByReceiptNumber(String receiptNumber);

    /**
     * 根据客户编号查询运输对账列表
     * 
     * @param customerCode 客户编号
     * @return 运输对账集合
     */
    public List<TransportReceipts> findByCustomerCode(String customerCode);

    /**
     * 查询客户期初余额
     * 
     * @param condition 查询条件
     * @return 期初余额
     */
    public BigDecimal getInitialAmount(TransportReceipts condition);

    /**
     * 批量保存运输对账
     * 
     * @param details 运输对账详情列表
     * @param createBy 创建人
     * @return 结果
     */
    public int batchSave(List<TransportReceiptsDetails> details, String createBy);

    /**
     * 查询运输对账数据用于导出
     * 
     * @param transportReceipts 查询条件
     * @return 运输对账集合
     */
    public List<TransportReceipts> listReceiptsReceiptsData(TransportReceipts transportReceipts);

    /**
     * 根据条件查询所有运输对账
     * 
     * @param condition 查询条件
     * @return 运输对账集合
     */
    public List<TransportReceipts> findAllByCondition(TransportReceipts condition);

    /**
     * 根据ID查询运输对账
     * 
     * @param id 主键ID
     * @return 运输对账
     */
    public TransportReceipts findById(Long id);

    /**
     * 插入运输对账
     * 
     * @param transportReceipts 运输对账
     * @return 结果
     */
    public int insert(TransportReceipts transportReceipts);

    /**
     * 删除运输对账
     * 
     * @param id 主键ID
     * @return 结果
     */
    public int delete(Long id);
}
