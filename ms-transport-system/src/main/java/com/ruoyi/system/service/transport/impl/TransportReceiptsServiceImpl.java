package com.ruoyi.system.service.transport.impl;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.dto.TransportReceiptsDetails;
import com.ruoyi.system.domain.transport.TransportReceipts;
import com.ruoyi.system.mapper.transport.TransportReceiptsMapper;
import com.ruoyi.system.service.transport.ITransportReceiptsService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 运输对账Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Service
public class TransportReceiptsServiceImpl implements ITransportReceiptsService {
    
    @Autowired
    private TransportReceiptsMapper transportReceiptsMapper;

    /**
     * 查询运输对账
     * 
     * @param id 运输对账主键
     * @return 运输对账
     */
    @Override
    public TransportReceipts selectTransportReceiptsById(Long id) {
        return transportReceiptsMapper.selectTransportReceiptsById(id);
    }

    /**
     * 查询运输对账列表
     * 
     * @param transportReceipts 运输对账
     * @return 运输对账
     */
    @Override
    public List<TransportReceipts> selectTransportReceiptsList(TransportReceipts transportReceipts) {
        return transportReceiptsMapper.selectTransportReceiptsList(transportReceipts);
    }

    /**
     * 新增运输对账
     * 
     * @param transportReceipts 运输对账
     * @return 结果
     */
    @Override
    public int insertTransportReceipts(TransportReceipts transportReceipts) {
        // 检查收据号码是否已存在
        if (StringUtils.isNotBlank(transportReceipts.getReceiptNumber())) {
            TransportReceipts existing = transportReceiptsMapper.selectByReceiptNumber(transportReceipts.getReceiptNumber());
            if (existing != null) {
                throw new ServiceException("收据号码已存在");
            }
        }
        
        transportReceipts.setCreateTime(DateUtils.getNowDate());
        return transportReceiptsMapper.insertTransportReceipts(transportReceipts);
    }

    /**
     * 修改运输对账
     * 
     * @param transportReceipts 运输对账
     * @return 结果
     */
    @Override
    public int updateTransportReceipts(TransportReceipts transportReceipts) {
        // 检查收据号码是否已存在（排除自己）
        if (StringUtils.isNotBlank(transportReceipts.getReceiptNumber())) {
            TransportReceipts existing = transportReceiptsMapper.selectByReceiptNumber(transportReceipts.getReceiptNumber());
            if (existing != null && !existing.getId().equals(transportReceipts.getId())) {
                throw new ServiceException("收据号码已存在");
            }
        }
        
        transportReceipts.setUpdateTime(DateUtils.getNowDate());
        return transportReceiptsMapper.updateTransportReceipts(transportReceipts);
    }

    /**
     * 批量删除运输对账
     * 
     * @param ids 需要删除的运输对账主键
     * @return 结果
     */
    @Override
    public int deleteTransportReceiptsByIds(Long[] ids) {
        return transportReceiptsMapper.deleteTransportReceiptsByIds(ids);
    }

    /**
     * 删除运输对账信息
     * 
     * @param id 运输对账主键
     * @return 结果
     */
    @Override
    public int deleteTransportReceiptsById(Long id) {
        return transportReceiptsMapper.deleteTransportReceiptsById(id);
    }

    /**
     * 根据收据号码查询运输对账
     * 
     * @param receiptNumber 收据号码
     * @return 运输对账
     */
    @Override
    public TransportReceipts findByReceiptNumber(String receiptNumber) {
        if (StringUtils.isBlank(receiptNumber)) {
            return null;
        }
        return transportReceiptsMapper.selectByReceiptNumber(receiptNumber);
    }

    /**
     * 根据客户编号查询运输对账列表
     * 
     * @param customerCode 客户编号
     * @return 运输对账集合
     */
    @Override
    public List<TransportReceipts> findByCustomerCode(String customerCode) {
        if (StringUtils.isBlank(customerCode)) {
            return new ArrayList<>();
        }
        return transportReceiptsMapper.selectByCustomerCode(customerCode);
    }

    /**
     * 查询客户期初余额
     * 
     * @param condition 查询条件
     * @return 期初余额
     */
    @Override
    public BigDecimal getInitialAmount(TransportReceipts condition) {
        if (condition == null || StringUtils.isBlank(condition.getCustomerCode())) {
            throw new ServiceException("客户编号不能为空");
        }
        
        BigDecimal initialAmount = transportReceiptsMapper.selectInitialAmountByCustomerCode(condition.getCustomerCode());
        return initialAmount != null ? initialAmount : BigDecimal.ZERO;
    }

    /**
     * 批量保存运输对账
     * 
     * @param details 运输对账详情列表
     * @param createBy 创建人
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchSave(List<TransportReceiptsDetails> details, String createBy) {
        if (details == null || details.isEmpty()) {
            throw new ServiceException("导入数据不能为空");
        }

        List<TransportReceipts> receiptsList = new ArrayList<>();
        for (TransportReceiptsDetails detail : details) {
            // 检查必填字段
            if (StringUtils.isBlank(detail.getReceiptNumber())) {
                throw new ServiceException("收据号码不能为空");
            }
            if (StringUtils.isBlank(detail.getCustomerCode())) {
                throw new ServiceException("客户编号不能为空");
            }
            if (StringUtils.isBlank(detail.getCustomerName())) {
                throw new ServiceException("客户名称不能为空");
            }

            // 检查收据号码是否已存在
            TransportReceipts existing = transportReceiptsMapper.selectByReceiptNumber(detail.getReceiptNumber());
            if (existing != null) {
                throw new ServiceException("收据号码 " + detail.getReceiptNumber() + " 已存在");
            }

            TransportReceipts receipts = new TransportReceipts();
            BeanUtils.copyProperties(detail, receipts);
            receipts.setCreateBy(createBy);
            receipts.setCreateTime(DateUtils.getNowDate());
            receipts.setIsDeleted(0);
            
            // 设置默认值
            if (receipts.getUsedAmount() == null) {
                receipts.setUsedAmount(BigDecimal.ZERO);
            }
            if (receipts.getTotalExpenseAmount() == null) {
                receipts.setTotalExpenseAmount(BigDecimal.ZERO);
            }
            if (receipts.getTotalArrearsAmount() == null) {
                receipts.setTotalArrearsAmount(BigDecimal.ZERO);
            }
            
            receiptsList.add(receipts);
        }

        return transportReceiptsMapper.batchInsertTransportReceipts(receiptsList);
    }

    /**
     * 查询运输对账数据用于导出
     * 
     * @param transportReceipts 查询条件
     * @return 运输对账集合
     */
    @Override
    public List<TransportReceipts> listReceiptsReceiptsData(TransportReceipts transportReceipts) {
        return transportReceiptsMapper.selectTransportReceiptsForExport(transportReceipts);
    }

    /**
     * 根据条件查询所有运输对账
     * 
     * @param condition 查询条件
     * @return 运输对账集合
     */
    @Override
    public List<TransportReceipts> findAllByCondition(TransportReceipts condition) {
        return transportReceiptsMapper.selectTransportReceiptsList(condition);
    }

    /**
     * 根据ID查询运输对账
     * 
     * @param id 主键ID
     * @return 运输对账
     */
    @Override
    public TransportReceipts findById(Long id) {
        if (id == null || id <= 0) {
            throw new ServiceException("ID不能为空");
        }
        
        TransportReceipts receipts = transportReceiptsMapper.selectTransportReceiptsById(id);
        if (receipts == null) {
            throw new ServiceException("运输对账信息不存在");
        }
        
        return receipts;
    }

    /**
     * 插入运输对账
     * 
     * @param transportReceipts 运输对账
     * @return 结果
     */
    @Override
    public int insert(TransportReceipts transportReceipts) {
        return insertTransportReceipts(transportReceipts);
    }

    /**
     * 删除运输对账
     * 
     * @param id 主键ID
     * @return 结果
     */
    @Override
    public int delete(Long id) {
        if (id == null || id <= 0) {
            throw new ServiceException("ID不能为空");
        }
        
        TransportReceipts receipts = transportReceiptsMapper.selectTransportReceiptsById(id);
        if (receipts == null) {
            throw new ServiceException("运输对账信息不存在");
        }
        
        return transportReceiptsMapper.deleteTransportReceiptsById(id);
    }
}
