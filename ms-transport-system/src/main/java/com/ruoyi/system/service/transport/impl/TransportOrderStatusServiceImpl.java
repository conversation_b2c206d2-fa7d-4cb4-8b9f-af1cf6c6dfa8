package com.ruoyi.system.service.transport.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.domain.transport.TransportOrder;
import com.ruoyi.system.mapper.transport.TransportOrderMapper;
import com.ruoyi.system.mapper.transport.TransportVehicleMapper;
import com.ruoyi.system.service.transport.ITransportOrderStatusService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * 运输单状态管理服务实现类
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Service
public class TransportOrderStatusServiceImpl implements ITransportOrderStatusService {
    
    @Autowired
    private TransportOrderMapper transportOrderMapper;
    
    @Autowired
    private TransportVehicleMapper transportVehicleMapper;

    /**
     * 检查状态流转是否合法
     * 
     * @param currentStatus 当前状态
     * @param targetStatus 目标状态
     * @return 是否合法
     */
    @Override
    public boolean isValidStatusTransition(Integer currentStatus, Integer targetStatus) {
        if (currentStatus == null || targetStatus == null) {
            return false;
        }
        
        // 定义合法的状态流转规则
        switch (currentStatus) {
            case 1: // 待指派
                return targetStatus == 2; // 只能到已指派
            case 2: // 已指派
                return targetStatus == 3; // 只能到前往装货
            case 3: // 前往装货
                return targetStatus == 4; // 只能到装货中
            case 4: // 装货中
                return targetStatus == 5; // 只能到运输中
            case 5: // 运输中
                return targetStatus == 6; // 只能到已送达
            case 6: // 已送达
                return targetStatus == 7; // 只能到已对账
            case 7: // 已对账
                return false; // 终态，不能再流转
            default:
                return false;
        }
    }

    /**
     * 更新运输单状态
     * 
     * @param orderId 运输单ID
     * @param targetStatus 目标状态
     * @return 结果
     */
    @Override
    @Transactional
    public int updateOrderStatus(Long orderId, Integer targetStatus) {
        // 1. 查询当前运输单
        TransportOrder order = transportOrderMapper.selectTransportOrderById(orderId);
        if (order == null) {
            throw new RuntimeException("运输单不存在");
        }
        
        // 2. 检查状态流转是否合法
        if (!isValidStatusTransition(order.getOrderStatus(), targetStatus)) {
            throw new RuntimeException("状态流转不合法，当前状态：" + getStatusDescription(order.getOrderStatus()) + 
                                     "，目标状态：" + getStatusDescription(targetStatus));
        }
        
        // 3. 更新状态
        int result = transportOrderMapper.updateOrderStatus(orderId, targetStatus, SecurityUtils.getUsername());
        
        // 4. 根据状态更新相关时间字段
        updateTimeFields(orderId, targetStatus);
        
        // 5. 更新车辆状态
        updateVehicleStatus(order, targetStatus);
        
        return result;
    }
    
    /**
     * 根据状态更新时间字段
     */
    private void updateTimeFields(Long orderId, Integer status) {
        Date now = DateUtils.getNowDate();
        
        switch (status) {
            case 4: // 装货中
                transportOrderMapper.updateActualLoadingTime(orderId, now);
                break;
            case 5: // 运输中
                transportOrderMapper.updateLoadingCompletedTime(orderId, now);
                transportOrderMapper.updateDepartureTime(orderId, now);
                break;
            case 6: // 已送达
                transportOrderMapper.updateArrivalTime(orderId, now);
                transportOrderMapper.updateActualDeliveryTime(orderId, now);
                break;
        }
    }
    
    /**
     * 更新车辆状态
     */
    private void updateVehicleStatus(TransportOrder order, Integer orderStatus) {
        if (order.getVehicleId() == null) {
            return;
        }
        
        Integer vehicleStatus;
        switch (orderStatus) {
            case 2: // 已指派
            case 3: // 前往装货
            case 4: // 装货中
            case 5: // 运输中
                vehicleStatus = 2; // 运输中
                break;
            case 6: // 已送达
            case 7: // 已对账
                vehicleStatus = 1; // 空闲
                break;
            default:
                return;
        }
        
        transportVehicleMapper.updateVehicleStatus(order.getVehicleId(), vehicleStatus, SecurityUtils.getUsername());
    }

    /**
     * 开始装货
     * 
     * @param orderId 运输单ID
     * @return 结果
     */
    @Override
    @Transactional
    public int startLoading(Long orderId) {
        return updateOrderStatus(orderId, OrderStatus.LOADING.getCode());
    }

    /**
     * 完成装货
     * 
     * @param orderId 运输单ID
     * @return 结果
     */
    @Override
    @Transactional
    public int completeLoading(Long orderId) {
        return updateOrderStatus(orderId, OrderStatus.TRANSPORTING.getCode());
    }

    /**
     * 开始运输
     * 
     * @param orderId 运输单ID
     * @return 结果
     */
    @Override
    @Transactional
    public int startTransporting(Long orderId) {
        return updateOrderStatus(orderId, OrderStatus.TRANSPORTING.getCode());
    }

    /**
     * 完成配送
     * 
     * @param orderId 运输单ID
     * @return 结果
     */
    @Override
    @Transactional
    public int completeDelivery(Long orderId) {
        return updateOrderStatus(orderId, OrderStatus.DELIVERED.getCode());
    }

    /**
     * 完成对账
     * 
     * @param orderId 运输单ID
     * @return 结果
     */
    @Override
    @Transactional
    public int completeBilling(Long orderId) {
        return updateOrderStatus(orderId, OrderStatus.BILLED.getCode());
    }

    /**
     * 获取状态描述
     * 
     * @param status 状态码
     * @return 状态描述
     */
    @Override
    public String getStatusDescription(Integer status) {
        if (status == null) {
            return "未知";
        }
        
        try {
            return OrderStatus.fromCode(status).getDesc();
        } catch (IllegalArgumentException e) {
            return "未知";
        }
    }

    /**
     * 获取下一个可能的状态列表
     * 
     * @param currentStatus 当前状态
     * @return 下一个可能的状态列表
     */
    @Override
    public OrderStatus[] getNextPossibleStatuses(Integer currentStatus) {
        if (currentStatus == null) {
            return new OrderStatus[0];
        }
        
        switch (currentStatus) {
            case 1: // 待指派
                return new OrderStatus[]{OrderStatus.ASSIGNED};
            case 2: // 已指派
                return new OrderStatus[]{OrderStatus.TO_LOADING};
            case 3: // 前往装货
                return new OrderStatus[]{OrderStatus.LOADING};
            case 4: // 装货中
                return new OrderStatus[]{OrderStatus.TRANSPORTING};
            case 5: // 运输中
                return new OrderStatus[]{OrderStatus.DELIVERED};
            case 6: // 已送达
                return new OrderStatus[]{OrderStatus.BILLED};
            case 7: // 已对账
                return new OrderStatus[0]; // 终态
            default:
                return new OrderStatus[0];
        }
    }
}
