package com.ruoyi.system.service.transport;

import com.ruoyi.system.domain.dto.TransportShippingRateDetails;
import com.ruoyi.system.domain.transport.TransportShippingRate;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * Excel处理服务接口
 *
 * <AUTHOR>
 */
public interface ITransportExcelProcessService {

    /**
     * 导出运费定价
     *
     * @param shippingRates 运费定价列表
     * @param response      HttpServletResponse
     */
    void exportShippingRate(List<TransportShippingRate> shippingRates, HttpServletResponse response);

    /**
     * 导入运费定价
     *
     * @param file Excel文件
     * @return 运费定价详情列表
     */
    List<TransportShippingRateDetails> importExcelForShippingRate(MultipartFile file);
}
