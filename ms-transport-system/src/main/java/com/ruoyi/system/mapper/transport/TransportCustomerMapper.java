package com.ruoyi.system.mapper.transport;

import com.ruoyi.system.domain.dto.TransportCustomerDropdownDto;
import com.ruoyi.system.domain.transport.TransportCustomer;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 委托客户信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Mapper
public interface TransportCustomerMapper {
    
    /**
     * 查询委托客户信息
     * 
     * @param id 委托客户信息主键
     * @return 委托客户信息
     */
    public TransportCustomer selectTransportCustomerById(Long id);

    /**
     * 查询委托客户信息列表
     * 
     * @param transportCustomer 委托客户信息
     * @return 委托客户信息集合
     */
    public List<TransportCustomer> selectTransportCustomerList(TransportCustomer transportCustomer);

    /**
     * 新增委托客户信息
     * 
     * @param transportCustomer 委托客户信息
     * @return 结果
     */
    public int insertTransportCustomer(TransportCustomer transportCustomer);

    /**
     * 修改委托客户信息
     * 
     * @param transportCustomer 委托客户信息
     * @return 结果
     */
    public int updateTransportCustomer(TransportCustomer transportCustomer);

    /**
     * 删除委托客户信息
     * 
     * @param id 委托客户信息主键
     * @return 结果
     */
    public int deleteTransportCustomerById(Long id);

    /**
     * 批量删除委托客户信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTransportCustomerByIds(Long[] ids);

    /**
     * 根据客户编码查询客户
     * 
     * @param customerCode 客户编码
     * @return 客户信息
     */
    public TransportCustomer selectCustomerByCode(@Param("customerCode") String customerCode);

    /**
     * 根据客户编码列表查询客户列表
     *
     * @param codes 客户编码列表
     * @return 客户信息列表
     */
    List<TransportCustomer> listCustomerByCodes(@Param("codes") List<String> codes);

    /**
     * 检查客户编码是否存在
     * 
     * @param customerCode 客户编码
     * @param id 客户ID(修改时排除自己)
     * @return 数量
     */
    public int checkCustomerCodeExists(@Param("customerCode") String customerCode, @Param("id") Long id);

    /**
     * 检查客户名称是否存在
     * 
     * @param customerName 客户名称
     * @param id 客户ID(修改时排除自己)
     * @return 数量
     */
    public int checkCustomerNameExists(@Param("customerName") String customerName, @Param("id") Long id);

    /**
     * 查询正常合作的客户列表
     * 
     * @return 客户信息集合
     */
    public List<TransportCustomer> selectActiveCustomers();

    /**
     * 更新客户状态
     * 
     * @param id 客户ID
     * @param status 新状态
     * @param updateBy 更新人
     * @return 结果
     */
    public int updateCustomerStatus(@Param("id") Long id, @Param("status") Integer status, @Param("updateBy") String updateBy);

    /**
     * 统计各状态客户数量
     * 
     * @return 统计结果
     */
    public List<TransportCustomer> selectCustomerStatusStatistics();

    /**
     * 根据信用等级查询客户
     * 
     * @param creditRating 信用等级
     * @return 客户信息集合
     */
    public List<TransportCustomer> selectCustomersByCreditRating(@Param("creditRating") String creditRating);

    /**
     * 根据付款方式查询客户
     * 
     * @param paymentMethod 付款方式
     * @return 客户信息集合
     */
    public List<TransportCustomer> selectCustomersByPaymentMethod(@Param("paymentMethod") Integer paymentMethod);

    /**
     * 查询VIP客户(有运费折扣的客户)
     * 
     * @return 客户信息集合
     */
    public List<TransportCustomer> selectVipCustomers();

    /**
     * 查询客户下拉列表
     *
     * @param keyword 搜索关键字 (客户编码或客户名称)
     * @return 客户下拉列表
     */
    public List<TransportCustomerDropdownDto> selectCustomerDropdownList(@Param("keyword") String keyword);

    /**
     * 根据ID批量查询客户信息列表
     *
     * @param ids 客户ID列表
     * @return 客户信息集合
     */
    List<TransportCustomer> selectCustomersByIds(@Param("ids") List<Long> ids);
}
