package com.ruoyi.system.mapper.transport;

import com.ruoyi.system.domain.transport.TransportReceipts;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 运输对账Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Mapper
public interface TransportReceiptsMapper {
    
    /**
     * 查询运输对账
     * 
     * @param id 运输对账主键
     * @return 运输对账
     */
    public TransportReceipts selectTransportReceiptsById(Long id);

    /**
     * 查询运输对账列表
     * 
     * @param transportReceipts 运输对账
     * @return 运输对账集合
     */
    public List<TransportReceipts> selectTransportReceiptsList(TransportReceipts transportReceipts);

    /**
     * 新增运输对账
     * 
     * @param transportReceipts 运输对账
     * @return 结果
     */
    public int insertTransportReceipts(TransportReceipts transportReceipts);

    /**
     * 修改运输对账
     * 
     * @param transportReceipts 运输对账
     * @return 结果
     */
    public int updateTransportReceipts(TransportReceipts transportReceipts);

    /**
     * 删除运输对账
     * 
     * @param id 运输对账主键
     * @return 结果
     */
    public int deleteTransportReceiptsById(Long id);

    /**
     * 批量删除运输对账
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTransportReceiptsByIds(Long[] ids);

    /**
     * 根据收据号码查询运输对账
     * 
     * @param receiptNumber 收据号码
     * @return 运输对账
     */
    public TransportReceipts selectByReceiptNumber(@Param("receiptNumber") String receiptNumber);

    /**
     * 根据客户编号查询运输对账列表
     * 
     * @param customerCode 客户编号
     * @return 运输对账集合
     */
    public List<TransportReceipts> selectByCustomerCode(@Param("customerCode") String customerCode);

    /**
     * 查询客户期初余额
     * 
     * @param customerCode 客户编号
     * @return 期初余额
     */
    public BigDecimal selectInitialAmountByCustomerCode(@Param("customerCode") String customerCode);

    /**
     * 批量插入运输对账
     * 
     * @param receiptsList 运输对账列表
     * @return 结果
     */
    public int batchInsertTransportReceipts(@Param("receiptsList") List<TransportReceipts> receiptsList);

    /**
     * 根据条件查询运输对账数据用于导出
     * 
     * @param transportReceipts 查询条件
     * @return 运输对账集合
     */
    public List<TransportReceipts> selectTransportReceiptsForExport(TransportReceipts transportReceipts);

    /**
     * 统计客户总金额
     * 
     * @param customerCode 客户编号
     * @return 总金额统计
     */
    public TransportReceipts selectCustomerAmountSummary(@Param("customerCode") String customerCode);
}
