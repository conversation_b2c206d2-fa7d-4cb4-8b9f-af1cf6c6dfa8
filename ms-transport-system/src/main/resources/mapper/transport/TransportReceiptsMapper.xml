<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.transport.TransportReceiptsMapper">
    
    <resultMap type="TransportReceipts" id="TransportReceiptsResult">
        <result property="id"                    column="id"                    />
        <result property="receiptNumber"         column="receipt_number"        />
        <result property="amount"                column="amount"                />
        <result property="fundType"              column="fund_type"             />
        <result property="description"           column="description"           />
        <result property="customerCode"          column="customer_code"         />
        <result property="internalCode"          column="internal_code"         />
        <result property="customerName"          column="customer_name"         />
        <result property="paymentMethod"         column="payment_method"        />
        <result property="bankName"              column="bank_name"             />
        <result property="receiptDate"           column="receipt_date"          />
        <result property="usedAmount"            column="used_amount"           />
        <result property="totalPrepaidAmount"    column="total_prepaid_amount"  />
        <result property="totalExpenseAmount"    column="total_expense_amount"  />
        <result property="totalArrearsAmount"    column="total_arrears_amount"  />
        <result property="createBy"              column="create_by"             />
        <result property="createTime"            column="create_time"           />
        <result property="updateBy"              column="update_by"             />
        <result property="updateTime"            column="update_time"           />
        <result property="isDeleted"             column="is_deleted"            />
    </resultMap>

    <sql id="selectTransportReceiptsVo">
        select id, receipt_number, amount, fund_type, description, customer_code, internal_code, 
               customer_name, payment_method, bank_name, receipt_date, used_amount, 
               total_prepaid_amount, total_expense_amount, total_arrears_amount, 
               create_by, create_time, update_by, update_time, is_deleted 
        from transport_receipts
    </sql>

    <select id="selectTransportReceiptsList" parameterType="TransportReceipts" resultMap="TransportReceiptsResult">
        <include refid="selectTransportReceiptsVo"/>
        <where>
            is_deleted = 0
            <if test="receiptNumber != null and receiptNumber != ''">
                and receipt_number like concat('%', #{receiptNumber}, '%')
            </if>
            <if test="customerCode != null and customerCode != ''">
                and customer_code = #{customerCode}
            </if>
            <if test="customerName != null and customerName != ''">
                and customer_name like concat('%', #{customerName}, '%')
            </if>
            <if test="fundType != null">
                and fund_type = #{fundType}
            </if>
            <if test="paymentMethod != null and paymentMethod != ''">
                and payment_method = #{paymentMethod}
            </if>
            <if test="startDate != null">
                and date(receipt_date) &gt;= date(#{startDate})
            </if>
            <if test="endDate != null">
                and date(receipt_date) &lt;= date(#{endDate})
            </if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectTransportReceiptsById" parameterType="Long" resultMap="TransportReceiptsResult">
        <include refid="selectTransportReceiptsVo"/>
        where id = #{id} and is_deleted = 0
    </select>

    <select id="selectByReceiptNumber" parameterType="String" resultMap="TransportReceiptsResult">
        <include refid="selectTransportReceiptsVo"/>
        where receipt_number = #{receiptNumber} and is_deleted = 0
    </select>

    <select id="selectByCustomerCode" parameterType="String" resultMap="TransportReceiptsResult">
        <include refid="selectTransportReceiptsVo"/>
        where customer_code = #{customerCode} and is_deleted = 0
        order by create_time desc
    </select>

    <select id="selectInitialAmountByCustomerCode" parameterType="String" resultType="java.math.BigDecimal">
        select COALESCE(SUM(
            CASE 
                WHEN fund_type = 0 THEN amount  -- 预收款为正
                WHEN fund_type = 1 THEN -amount -- 应付款为负
                WHEN fund_type = 2 THEN amount  -- 红冲应收为正
                WHEN fund_type = 3 THEN -amount -- 红冲应付为负
                ELSE 0
            END
        ), 0) as initial_amount
        from transport_receipts
        where customer_code = #{customerCode} and is_deleted = 0
    </select>

    <select id="selectTransportReceiptsForExport" parameterType="TransportReceipts" resultMap="TransportReceiptsResult">
        <include refid="selectTransportReceiptsVo"/>
        <where>
            is_deleted = 0
            <if test="customerCode != null and customerCode != ''">
                and customer_code = #{customerCode}
            </if>
            <if test="startDate != null">
                and date(receipt_date) &gt;= date(#{startDate})
            </if>
            <if test="endDate != null">
                and date(receipt_date) &lt;= date(#{endDate})
            </if>
        </where>
        order by receipt_date desc, create_time desc
    </select>

    <select id="selectCustomerAmountSummary" parameterType="String" resultMap="TransportReceiptsResult">
        select 
            customer_code,
            customer_name,
            SUM(CASE WHEN fund_type = 0 THEN amount ELSE 0 END) as total_prepaid_amount,
            SUM(CASE WHEN fund_type = 1 THEN amount ELSE 0 END) as total_expense_amount,
            SUM(CASE WHEN fund_type IN (2,3) THEN amount ELSE 0 END) as total_arrears_amount,
            SUM(used_amount) as used_amount
        from transport_receipts
        where customer_code = #{customerCode} and is_deleted = 0
        group by customer_code, customer_name
    </select>
        
    <insert id="insertTransportReceipts" parameterType="TransportReceipts" useGeneratedKeys="true" keyProperty="id">
        insert into transport_receipts
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="receiptNumber != null and receiptNumber != ''">receipt_number,</if>
            <if test="amount != null">amount,</if>
            <if test="fundType != null">fund_type,</if>
            <if test="description != null">description,</if>
            <if test="customerCode != null and customerCode != ''">customer_code,</if>
            <if test="internalCode != null">internal_code,</if>
            <if test="customerName != null and customerName != ''">customer_name,</if>
            <if test="paymentMethod != null">payment_method,</if>
            <if test="bankName != null">bank_name,</if>
            <if test="receiptDate != null">receipt_date,</if>
            <if test="usedAmount != null">used_amount,</if>
            <if test="totalPrepaidAmount != null">total_prepaid_amount,</if>
            <if test="totalExpenseAmount != null">total_expense_amount,</if>
            <if test="totalArrearsAmount != null">total_arrears_amount,</if>
            <if test="createBy != null">create_by,</if>
            create_time,
            is_deleted
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="receiptNumber != null and receiptNumber != ''">#{receiptNumber},</if>
            <if test="amount != null">#{amount},</if>
            <if test="fundType != null">#{fundType},</if>
            <if test="description != null">#{description},</if>
            <if test="customerCode != null and customerCode != ''">#{customerCode},</if>
            <if test="internalCode != null">#{internalCode},</if>
            <if test="customerName != null and customerName != ''">#{customerName},</if>
            <if test="paymentMethod != null">#{paymentMethod},</if>
            <if test="bankName != null">#{bankName},</if>
            <if test="receiptDate != null">#{receiptDate},</if>
            <if test="usedAmount != null">#{usedAmount},</if>
            <if test="totalPrepaidAmount != null">#{totalPrepaidAmount},</if>
            <if test="totalExpenseAmount != null">#{totalExpenseAmount},</if>
            <if test="totalArrearsAmount != null">#{totalArrearsAmount},</if>
            <if test="createBy != null">#{createBy},</if>
            sysdate(),
            0
        </trim>
    </insert>

    <update id="updateTransportReceipts" parameterType="TransportReceipts">
        update transport_receipts
        <trim prefix="SET" suffixOverrides=",">
            <if test="receiptNumber != null and receiptNumber != ''">receipt_number = #{receiptNumber},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="fundType != null">fund_type = #{fundType},</if>
            <if test="description != null">description = #{description},</if>
            <if test="customerCode != null and customerCode != ''">customer_code = #{customerCode},</if>
            <if test="internalCode != null">internal_code = #{internalCode},</if>
            <if test="customerName != null and customerName != ''">customer_name = #{customerName},</if>
            <if test="paymentMethod != null">payment_method = #{paymentMethod},</if>
            <if test="bankName != null">bank_name = #{bankName},</if>
            <if test="receiptDate != null">receipt_date = #{receiptDate},</if>
            <if test="usedAmount != null">used_amount = #{usedAmount},</if>
            <if test="totalPrepaidAmount != null">total_prepaid_amount = #{totalPrepaidAmount},</if>
            <if test="totalExpenseAmount != null">total_expense_amount = #{totalExpenseAmount},</if>
            <if test="totalArrearsAmount != null">total_arrears_amount = #{totalArrearsAmount},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTransportReceiptsById" parameterType="Long">
        update transport_receipts set is_deleted = 1 where id = #{id}
    </delete>

    <delete id="deleteTransportReceiptsByIds" parameterType="String">
        update transport_receipts set is_deleted = 1 where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsertTransportReceipts" parameterType="java.util.List">
        insert into transport_receipts (
            receipt_number, amount, fund_type, description, customer_code, internal_code,
            customer_name, payment_method, bank_name, receipt_date, used_amount,
            total_prepaid_amount, total_expense_amount, total_arrears_amount,
            create_by, create_time, is_deleted
        ) values
        <foreach collection="receiptsList" item="item" separator=",">
            (
                #{item.receiptNumber}, #{item.amount}, #{item.fundType}, #{item.description},
                #{item.customerCode}, #{item.internalCode}, #{item.customerName},
                #{item.paymentMethod}, #{item.bankName}, #{item.receiptDate}, #{item.usedAmount},
                #{item.totalPrepaidAmount}, #{item.totalExpenseAmount}, #{item.totalArrearsAmount},
                #{item.createBy}, #{item.createTime}, 0
            )
        </foreach>
    </insert>
    
</mapper>
