-- ========================================
-- 运输管理系统完整建表语句
-- 版本: 1.0
-- 创建时间: 2024-01-15
-- 说明: 包含运输管理系统所有核心业务表
-- ========================================

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ========================================
-- 1. 地址管理相关表
-- ========================================

-- 运输管理国家表
DROP TABLE IF EXISTS `transport_countries`;
CREATE TABLE `transport_countries` (
  `code` varchar(10) NOT NULL COMMENT '国家编码',
  `name` varchar(100) NOT NULL COMMENT '国家名称',
  `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(100) DEFAULT NULL COMMENT '修改人',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`code`),
  KEY `idx_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='运输管理国家表';

-- 运输管理省份表
DROP TABLE IF EXISTS `transport_provinces`;
CREATE TABLE `transport_provinces` (
  `code` varchar(10) NOT NULL COMMENT '省份编码',
  `name` varchar(100) NOT NULL COMMENT '省份名称',
  `country_code` varchar(10) NOT NULL COMMENT '国家编码',
  `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(100) DEFAULT NULL COMMENT '修改人',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`code`),
  KEY `idx_country_code` (`country_code`),
  KEY `idx_name` (`name`),
  CONSTRAINT `fk_provinces_country` FOREIGN KEY (`country_code`) REFERENCES `transport_countries` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='运输管理省份表';

-- 运输管理城市表
DROP TABLE IF EXISTS `transport_cities`;
CREATE TABLE `transport_cities` (
  `code` varchar(10) NOT NULL COMMENT '城市编码',
  `name` varchar(100) NOT NULL COMMENT '城市名称',
  `province_code` varchar(10) NOT NULL COMMENT '省份编码',
  `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(100) DEFAULT NULL COMMENT '修改人',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`code`),
  KEY `idx_province_code` (`province_code`),
  KEY `idx_name` (`name`),
  CONSTRAINT `fk_cities_province` FOREIGN KEY (`province_code`) REFERENCES `transport_provinces` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='运输管理城市表';

-- 运输管理区县表
DROP TABLE IF EXISTS `transport_districts`;
CREATE TABLE `transport_districts` (
  `code` varchar(10) NOT NULL COMMENT '区县编码',
  `name` varchar(100) NOT NULL COMMENT '区县名称',
  `city_code` varchar(10) NOT NULL COMMENT '城市编码',
  `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(100) DEFAULT NULL COMMENT '修改人',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`code`),
  KEY `idx_city_code` (`city_code`),
  KEY `idx_name` (`name`),
  CONSTRAINT `fk_districts_city` FOREIGN KEY (`city_code`) REFERENCES `transport_cities` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='运输管理区县表';

-- ========================================
-- 2. 车辆管理相关表
-- ========================================

-- 车辆信息表
DROP TABLE IF EXISTS `transport_vehicle`;
CREATE TABLE `transport_vehicle` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '唯一标识ID',
    `license_plate` varchar(20) NOT NULL COMMENT '车牌号',
    `vehicle_type` varchar(50) COMMENT '车辆类型',
    `vehicle_model` varchar(100) COMMENT '车辆型号',
    `load_capacity` decimal(8,2) COMMENT '载重吨位',
    `fuel_tank_capacity` decimal(8,2) COMMENT '油箱容量(升)',
    `vehicle_status` tinyint(4) DEFAULT 1 COMMENT '车辆状态:1-空闲,2-运输中,3-维修中,4-报废',
    `purchase_date` date COMMENT '购买日期',
    `registration_date` date COMMENT '注册日期',
    `insurance_expiry` date COMMENT '保险到期日',
    `annual_inspection_date` date COMMENT '年检日期',
    `region_code` varchar(20) COMMENT '所属区域编码',
    `owner_name` varchar(100) COMMENT '车主姓名',
    `owner_phone` varchar(20) COMMENT '车主电话',
    `remark` text COMMENT '备注',
    `create_by` varchar(100) COMMENT '创建人',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` varchar(100) COMMENT '修改人',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `is_deleted` tinyint(1) DEFAULT 0 NOT NULL COMMENT '逻辑删除标志，0表示未删除，1表示已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_license_plate` (`license_plate`),
    KEY `idx_vehicle_status` (`vehicle_status`),
    KEY `idx_region_code` (`region_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='车辆信息表';

-- ========================================
-- 3. 司机管理相关表
-- ========================================

-- 司机信息表
DROP TABLE IF EXISTS `transport_driver`;
CREATE TABLE `transport_driver` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '唯一标识ID',
    `driver_name` varchar(50) NOT NULL COMMENT '司机姓名',
    `driver_phone` varchar(20) COMMENT '联系电话',
    `id_card` varchar(18) COMMENT '身份证号',
    `license_type` varchar(10) COMMENT '驾照类型',
    `license_number` varchar(50) COMMENT '驾照号码',
    `license_expiry` date COMMENT '驾照到期日',
    `driving_years` int(11) COMMENT '驾龄(年)',
    `driver_status` tinyint(4) DEFAULT 1 COMMENT '司机状态:1-在职,2-休假,3-离职',
    `hire_date` date COMMENT '入职日期',
    `emergency_contact` varchar(50) COMMENT '紧急联系人',
    `emergency_phone` varchar(20) COMMENT '紧急联系电话',
    `address` varchar(500) COMMENT '家庭住址',
    `remark` text COMMENT '备注',
    `create_by` varchar(100) COMMENT '创建人',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` varchar(100) COMMENT '修改人',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `is_deleted` tinyint(1) DEFAULT 0 NOT NULL COMMENT '逻辑删除标志，0表示未删除，1表示已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_driver_phone` (`driver_phone`),
    UNIQUE KEY `uk_id_card` (`id_card`),
    KEY `idx_driver_status` (`driver_status`),
    KEY `idx_license_type` (`license_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='司机信息表';

-- 司机排班表
DROP TABLE IF EXISTS `transport_driver_schedule`;
CREATE TABLE `transport_driver_schedule` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '唯一标识ID',
    `driver_id` bigint(20) NOT NULL COMMENT '司机ID',
    `work_date` date COMMENT '工作日期',
    `shift_type` tinyint(4) COMMENT '班次类型:1-白班,2-夜班,3-休息',
    `work_start_time` time COMMENT '上班时间',
    `work_end_time` time COMMENT '下班时间',
    `actual_work_hours` decimal(4,2) COMMENT '实际工作小时',
    `create_by` varchar(100) COMMENT '创建人',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` varchar(100) COMMENT '修改人',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `is_deleted` tinyint(1) DEFAULT 0 NOT NULL COMMENT '逻辑删除标志，0表示未删除，1表示已删除',
    PRIMARY KEY (`id`),
    KEY `idx_driver_id` (`driver_id`),
    KEY `idx_work_date` (`work_date`),
    CONSTRAINT `fk_schedule_driver` FOREIGN KEY (`driver_id`) REFERENCES `transport_driver` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='司机排班表';

-- ========================================
-- 4. 客户管理相关表
-- ========================================

-- 客户信息表
DROP TABLE IF EXISTS `transport_customer`;
CREATE TABLE `transport_customer` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '唯一标识ID',
    `customer_name` varchar(255) NOT NULL COMMENT '客户名称',
    `customer_code` varchar(50) COMMENT '客户编码',
    `company_type` varchar(100) COMMENT '公司类型',
    `contact_person` varchar(100) COMMENT '联系人',
    `contact_phone` varchar(20) COMMENT '联系电话',
    `contact_email` varchar(100) COMMENT '联系邮箱',
    `company_address` varchar(500) COMMENT '公司地址',
    `customer_status` tinyint(4) DEFAULT 1 COMMENT '客户状态:1-正常合作,2-暂停合作,3-终止合作',
    `credit_rating` varchar(10) COMMENT '信用等级:AAA,AA,A,B,C',
    `payment_method` tinyint(4) COMMENT '付款方式:1-月结,2-现结,3-预付',
    `payment_days` int(11) COMMENT '账期天数',
    `is_vip` tinyint(1) DEFAULT 0 COMMENT '是否VIP客户',
    `remark` text COMMENT '备注',
    `create_by` varchar(100) COMMENT '创建人',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` varchar(100) COMMENT '修改人',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `is_deleted` tinyint(1) DEFAULT 0 NOT NULL COMMENT '逻辑删除标志，0表示未删除，1表示已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_customer_code` (`customer_code`),
    UNIQUE KEY `uk_customer_name` (`customer_name`),
    KEY `idx_customer_status` (`customer_status`),
    KEY `idx_credit_rating` (`credit_rating`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户信息表';

-- ========================================
-- 5. 收货方管理相关表
-- ========================================

-- 收货方信息表
DROP TABLE IF EXISTS `transport_consignee`;
CREATE TABLE `transport_consignee` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '唯一标识ID',
    `consignee_name` varchar(255) NOT NULL COMMENT '收货方名称',
    `consignee_code` varchar(50) COMMENT '收货方编码',
    `customer_id` bigint(20) COMMENT '所属客户ID',
    `contact_person` varchar(100) COMMENT '联系人',
    `contact_phone` varchar(20) COMMENT '联系电话',
    `province_code` varchar(10) COMMENT '省份编码',
    `province_name` varchar(50) COMMENT '省份名称',
    `city_code` varchar(10) COMMENT '城市编码',
    `city_name` varchar(50) COMMENT '城市名称',
    `district_code` varchar(10) COMMENT '区县编码',
    `district_name` varchar(50) COMMENT '区县名称',
    `detail_address` varchar(500) COMMENT '详细地址',
    `consignee_type` tinyint(4) COMMENT '收货方类型:1-加油站,2-工厂,3-仓库,4-其他',
    `business_hours` varchar(100) COMMENT '营业时间',
    `special_requirements` text COMMENT '特殊要求',
    `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
    `remark` text COMMENT '备注',
    `create_by` varchar(100) COMMENT '创建人',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` varchar(100) COMMENT '修改人',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `is_deleted` tinyint(1) DEFAULT 0 NOT NULL COMMENT '逻辑删除标志，0表示未删除，1表示已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_consignee_code` (`consignee_code`),
    KEY `idx_customer_id` (`customer_id`),
    KEY `idx_consignee_type` (`consignee_type`),
    KEY `idx_province_code` (`province_code`),
    CONSTRAINT `fk_consignee_customer` FOREIGN KEY (`customer_id`) REFERENCES `transport_customer` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='收货方信息表';

-- ========================================
-- 6. 装货点管理相关表
-- ========================================

-- 装货点信息表
DROP TABLE IF EXISTS `transport_loading_point`;
CREATE TABLE `transport_loading_point` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '唯一标识ID',
    `point_name` varchar(255) NOT NULL COMMENT '装货点名称',
    `point_code` varchar(50) COMMENT '装货点编码',
    `point_type` tinyint(4) COMMENT '装货点类型:1-港口,2-油库,3-炼厂,4-其他',
    `province_code` varchar(10) COMMENT '省份编码',
    `province_name` varchar(50) COMMENT '省份名称',
    `city_code` varchar(10) COMMENT '城市编码',
    `city_name` varchar(50) COMMENT '城市名称',
    `district_code` varchar(10) COMMENT '区县编码',
    `district_name` varchar(50) COMMENT '区县名称',
    `detail_address` varchar(500) COMMENT '详细地址',
    `contact_person` varchar(100) COMMENT '联系人',
    `contact_phone` varchar(20) COMMENT '联系电话',
    `operating_hours` varchar(100) COMMENT '营业时间',
    `loading_capacity` varchar(200) COMMENT '装载能力',
    `special_requirements` text COMMENT '特殊要求',
    `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
    `remark` text COMMENT '备注',
    `create_by` varchar(100) COMMENT '创建人',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` varchar(100) COMMENT '修改人',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `is_deleted` tinyint(1) DEFAULT 0 NOT NULL COMMENT '逻辑删除标志，0表示未删除，1表示已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_point_code` (`point_code`),
    KEY `idx_point_type` (`point_type`),
    KEY `idx_province_code` (`province_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='装货点信息表';

-- ========================================
-- 7. 运输单管理相关表
-- ========================================

-- 运输单主表
DROP TABLE IF EXISTS `transport_order`;
CREATE TABLE `transport_order` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '唯一标识ID',
    `order_no` varchar(32) NOT NULL COMMENT '运输单号',
    `internal_code` varchar(32) COMMENT '内部编号',
    `customer_id` bigint(20) COMMENT '客户ID',
    `customer_name` varchar(255) COMMENT '客户名称',
    `consignee_id` bigint(20) COMMENT '收货方ID',
    `consignee_name` varchar(255) COMMENT '收货方名称',
    `consignee_address` varchar(500) COMMENT '收货地址',
    `consignee_contact` varchar(100) COMMENT '收货联系人',
    `consignee_phone` varchar(20) COMMENT '收货联系电话',
    `loading_point_id` bigint(20) COMMENT '装货点ID',
    `loading_point_name` varchar(255) COMMENT '装货点名称',
    `loading_address` varchar(500) COMMENT '装货地址',
    `product_name` varchar(255) COMMENT '油品名称',
    `product_quantity` decimal(10,2) COMMENT '运输数量(吨)',
    `total_volume` decimal(12,2) COMMENT '总体积(升)',
    `transport_distance` decimal(8,2) COMMENT '运输距离(公里)',
    `order_status` tinyint(4) DEFAULT 1 COMMENT '运输单状态:1-待指派,2-已指派,3-前往装货,4-装货中,5-运输中,6-已送达,7-已对账',
    `vehicle_id` bigint(20) COMMENT '车辆ID',
    `vehicle_license` varchar(20) COMMENT '车牌号',
    `driver_id` bigint(20) COMMENT '司机ID',
    `driver_name` varchar(50) COMMENT '司机姓名',
    `driver_phone` varchar(20) COMMENT '司机电话',
    `planned_loading_time` datetime COMMENT '计划装货时间',
    `planned_delivery_time` datetime COMMENT '计划送达时间',
    `actual_loading_time` datetime COMMENT '实际装货时间',
    `actual_departure_time` datetime COMMENT '实际出发时间',
    `actual_delivery_time` datetime COMMENT '实际送达时间',
    `assign_time` datetime COMMENT '指派时间',
    `assign_by` varchar(100) COMMENT '指派人',
    `shipping_cost` decimal(10,2) COMMENT '运输费用',
    `other_expenses` decimal(10,2) COMMENT '其他费用',
    `total_cost` decimal(10,2) COMMENT '总费用',
    `billing_time` datetime COMMENT '对账时间',
    `billing_by` varchar(100) COMMENT '对账人',
    `delivery_requirements` text COMMENT '配送要求',
    `special_instructions` text COMMENT '特殊说明',
    `remark` text COMMENT '备注',
    `create_by` varchar(100) COMMENT '创建人',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` varchar(100) COMMENT '修改人',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `is_deleted` tinyint(1) DEFAULT 0 NOT NULL COMMENT '逻辑删除标志，0表示未删除，1表示已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_order_no` (`order_no`),
    UNIQUE KEY `uk_internal_code` (`internal_code`),
    KEY `idx_customer_id` (`customer_id`),
    KEY `idx_order_status` (`order_status`),
    KEY `idx_vehicle_id` (`vehicle_id`),
    KEY `idx_driver_id` (`driver_id`),
    KEY `idx_create_time` (`create_time`),
    CONSTRAINT `fk_order_customer` FOREIGN KEY (`customer_id`) REFERENCES `transport_customer` (`id`),
    CONSTRAINT `fk_order_vehicle` FOREIGN KEY (`vehicle_id`) REFERENCES `transport_vehicle` (`id`),
    CONSTRAINT `fk_order_driver` FOREIGN KEY (`driver_id`) REFERENCES `transport_driver` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='运输单主表';

-- 运输单状态变更记录表
DROP TABLE IF EXISTS `transport_order_status_log`;
CREATE TABLE `transport_order_status_log` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '唯一标识ID',
    `order_id` bigint(20) NOT NULL COMMENT '运输单ID',
    `order_no` varchar(32) COMMENT '运输单号',
    `old_status` tinyint(4) COMMENT '原状态',
    `new_status` tinyint(4) COMMENT '新状态',
    `status_change_time` datetime COMMENT '状态变更时间',
    `change_reason` varchar(500) COMMENT '变更原因',
    `operator` varchar(100) COMMENT '操作人',
    `remark` text COMMENT '备注',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_order_id` (`order_id`),
    KEY `idx_order_no` (`order_no`),
    KEY `idx_status_change_time` (`status_change_time`),
    CONSTRAINT `fk_status_log_order` FOREIGN KEY (`order_id`) REFERENCES `transport_order` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='运输单状态变更记录表';

-- ========================================
-- 8. 运费规则管理相关表
-- ========================================

-- 运费规则表
DROP TABLE IF EXISTS `transport_pricing_rule`;
CREATE TABLE `transport_pricing_rule` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '唯一标识ID',
    `rule_name` varchar(100) NOT NULL COMMENT '规则名称',
    `customer_id` bigint(20) COMMENT '适用客户ID(为空表示通用规则)',
    `route_id` bigint(20) COMMENT '适用路线ID(为空表示通用规则)',
    `product_type` varchar(50) COMMENT '适用油品类型',
    `pricing_type` tinyint(4) NOT NULL COMMENT '计费类型:1-按距离,2-按重量,3-按体积,4-固定价格',
    `base_price` decimal(10,2) DEFAULT 0 COMMENT '基础价格',
    `unit_price` decimal(10,4) COMMENT '单位价格(元/公里 或 元/吨 或 元/升)',
    `min_price` decimal(10,2) COMMENT '最低收费',
    `max_price` decimal(10,2) COMMENT '最高收费',
    `effective_date` date COMMENT '生效日期',
    `expiry_date` date COMMENT '失效日期',
    `priority` int(11) DEFAULT 0 COMMENT '优先级(数字越大优先级越高)',
    `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
    `remark` text COMMENT '备注',
    `create_by` varchar(100) COMMENT '创建人',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` varchar(100) COMMENT '修改人',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `is_deleted` tinyint(1) DEFAULT 0 NOT NULL COMMENT '逻辑删除标志，0表示未删除，1表示已删除',
    PRIMARY KEY (`id`),
    KEY `idx_customer_id` (`customer_id`),
    KEY `idx_pricing_type` (`pricing_type`),
    KEY `idx_effective_date` (`effective_date`),
    KEY `idx_priority` (`priority`),
    CONSTRAINT `fk_pricing_customer` FOREIGN KEY (`customer_id`) REFERENCES `transport_customer` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='运费规则表';

-- 附加费用规则表
DROP TABLE IF EXISTS `transport_additional_fee`;
CREATE TABLE `transport_additional_fee` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '唯一标识ID',
    `fee_name` varchar(50) NOT NULL COMMENT '费用名称',
    `fee_type` varchar(20) COMMENT '费用类型:过夜费,高速费,偏远地区费,装卸费',
    `calculation_method` tinyint(4) COMMENT '计算方式:1-固定金额,2-按比例,3-按距离,4-按重量',
    `fee_amount` decimal(10,2) COMMENT '费用金额或比例',
    `condition_desc` text COMMENT '收费条件描述',
    `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
    `create_by` varchar(100) COMMENT '创建人',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` varchar(100) COMMENT '修改人',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `is_deleted` tinyint(1) DEFAULT 0 NOT NULL COMMENT '逻辑删除标志，0表示未删除，1表示已删除',
    PRIMARY KEY (`id`),
    KEY `idx_fee_type` (`fee_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='附加费用规则表';

-- ========================================
-- 9. 仓库管理相关表
-- ========================================

-- 仓库信息表
DROP TABLE IF EXISTS `transport_warehouse`;
CREATE TABLE `transport_warehouse` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '唯一标识ID',
    `warehouse_name` varchar(255) NOT NULL COMMENT '仓库名称',
    `warehouse_code` varchar(50) COMMENT '仓库编码',
    `warehouse_address` text COMMENT '仓库地址',
    `warehouse_type` tinyint(4) DEFAULT 1 COMMENT '仓库类型:1-油库,2-中转库,3-临时库',
    `warehouse_status` tinyint(4) DEFAULT 1 COMMENT '仓库状态:1-正常,2-维护中,3-停用',
    `storage_capacity` decimal(15,2) COMMENT '总存储容量(升)',
    `manager_name` varchar(100) COMMENT '仓库管理员',
    `manager_phone` varchar(20) COMMENT '管理员电话',
    `province_code` varchar(10) COMMENT '省份编码',
    `province_name` varchar(50) COMMENT '省份名称',
    `city_code` varchar(10) COMMENT '城市编码',
    `city_name` varchar(50) COMMENT '城市名称',
    `remark` text COMMENT '备注',
    `create_by` varchar(100) COMMENT '创建人',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` varchar(100) COMMENT '修改人',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `is_deleted` tinyint(1) DEFAULT 0 NOT NULL COMMENT '逻辑删除标志，0表示未删除，1表示已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_warehouse_code` (`warehouse_code`),
    KEY `idx_warehouse_type` (`warehouse_type`),
    KEY `idx_warehouse_status` (`warehouse_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='仓库信息表';

-- 商品信息表
DROP TABLE IF EXISTS `transport_product`;
CREATE TABLE `transport_product` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '唯一标识ID',
    `product_name` varchar(255) NOT NULL COMMENT '商品名称',
    `product_code` varchar(50) COMMENT '商品编码',
    `product_type` varchar(100) COMMENT '商品类型',
    `product_spec` varchar(100) COMMENT '商品规格',
    `unit` varchar(20) DEFAULT '升' COMMENT '计量单位',
    `density` decimal(8,4) COMMENT '密度(kg/L)',
    `safety_stock` decimal(15,2) DEFAULT 0 COMMENT '安全库存',
    `product_status` tinyint(4) DEFAULT 1 COMMENT '商品状态:1-正常,2-停用',
    `remark` text COMMENT '备注',
    `create_by` varchar(100) COMMENT '创建人',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` varchar(100) COMMENT '修改人',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `is_deleted` tinyint(1) DEFAULT 0 NOT NULL COMMENT '逻辑删除标志，0表示未删除，1表示已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_product_code` (`product_code`),
    KEY `idx_product_type` (`product_type`),
    KEY `idx_product_status` (`product_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品信息表';

-- 库存信息表
DROP TABLE IF EXISTS `transport_inventory`;
CREATE TABLE `transport_inventory` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '唯一标识ID',
    `warehouse_id` bigint(20) NOT NULL COMMENT '仓库ID',
    `product_id` bigint(20) NOT NULL COMMENT '商品ID',
    `current_stock` decimal(15,2) DEFAULT 0 COMMENT '当前库存',
    `available_stock` decimal(15,2) DEFAULT 0 COMMENT '可用库存',
    `frozen_stock` decimal(15,2) DEFAULT 0 COMMENT '冻结库存',
    `last_in_time` timestamp NULL COMMENT '最后入库时间',
    `last_out_time` timestamp NULL COMMENT '最后出库时间',
    `create_by` varchar(100) COMMENT '创建人',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` varchar(100) COMMENT '修改人',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `is_deleted` tinyint(1) DEFAULT 0 NOT NULL COMMENT '逻辑删除标志，0表示未删除，1表示已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_warehouse_product` (`warehouse_id`, `product_id`),
    KEY `idx_warehouse_id` (`warehouse_id`),
    KEY `idx_product_id` (`product_id`),
    CONSTRAINT `fk_inventory_warehouse` FOREIGN KEY (`warehouse_id`) REFERENCES `transport_warehouse` (`id`),
    CONSTRAINT `fk_inventory_product` FOREIGN KEY (`product_id`) REFERENCES `transport_product` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存信息表';

-- 出入库记录主表
DROP TABLE IF EXISTS `transport_inventory_record`;
CREATE TABLE `transport_inventory_record` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '唯一标识ID',
    `record_no` varchar(32) NOT NULL COMMENT '出入库单号',
    `warehouse_id` bigint(20) NOT NULL COMMENT '仓库ID',
    `record_type` tinyint(4) NOT NULL COMMENT '单据类型:1-入库,2-出库,3-调拨,4-盘点',
    `business_type` tinyint(4) COMMENT '业务类型:1-采购入库,2-生产入库,3-销售出库,4-运输出库,5-其他',
    `related_order_no` varchar(50) COMMENT '关联单号(运输单号等)',
    `record_status` tinyint(4) DEFAULT 1 COMMENT '单据状态:1-待审核,2-已审核,3-已完成,4-已取消',
    `total_quantity` decimal(15,2) DEFAULT 0 COMMENT '总数量',
    `operator_name` varchar(100) COMMENT '操作员',
    `audit_by` varchar(100) COMMENT '审核人',
    `audit_time` timestamp NULL COMMENT '审核时间',
    `remark` text COMMENT '备注',
    `create_by` varchar(100) COMMENT '创建人',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` varchar(100) COMMENT '修改人',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `is_deleted` tinyint(1) DEFAULT 0 NOT NULL COMMENT '逻辑删除标志，0表示未删除，1表示已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_record_no` (`record_no`),
    KEY `idx_warehouse_id` (`warehouse_id`),
    KEY `idx_record_type` (`record_type`),
    KEY `idx_record_status` (`record_status`),
    KEY `idx_related_order_no` (`related_order_no`),
    CONSTRAINT `fk_record_warehouse` FOREIGN KEY (`warehouse_id`) REFERENCES `transport_warehouse` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='出入库记录主表';

-- 出入库记录明细表
DROP TABLE IF EXISTS `transport_inventory_record_detail`;
CREATE TABLE `transport_inventory_record_detail` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '唯一标识ID',
    `record_id` bigint(20) NOT NULL COMMENT '出入库单ID',
    `product_id` bigint(20) NOT NULL COMMENT '商品ID',
    `quantity` decimal(15,2) NOT NULL COMMENT '数量',
    `unit_price` decimal(10,4) COMMENT '单价',
    `total_amount` decimal(15,2) COMMENT '总金额',
    `batch_no` varchar(50) COMMENT '批次号',
    `production_date` date COMMENT '生产日期',
    `expiry_date` date COMMENT '有效期',
    `remark` text COMMENT '备注',
    `create_by` varchar(100) COMMENT '创建人',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` varchar(100) COMMENT '修改人',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `is_deleted` tinyint(1) DEFAULT 0 NOT NULL COMMENT '逻辑删除标志，0表示未删除，1表示已删除',
    PRIMARY KEY (`id`),
    KEY `idx_record_id` (`record_id`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_batch_no` (`batch_no`),
    CONSTRAINT `fk_detail_record` FOREIGN KEY (`record_id`) REFERENCES `transport_inventory_record` (`id`),
    CONSTRAINT `fk_detail_product` FOREIGN KEY (`product_id`) REFERENCES `transport_product` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='出入库记录明细表';

-- ========================================
-- 10. 系统配置相关表
-- ========================================

-- 系统参数配置表
DROP TABLE IF EXISTS `transport_system_config`;
CREATE TABLE `transport_system_config` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '唯一标识ID',
    `config_key` varchar(100) NOT NULL COMMENT '配置键',
    `config_value` text COMMENT '配置值',
    `config_desc` varchar(500) COMMENT '配置描述',
    `config_type` varchar(50) COMMENT '配置类型',
    `is_system` tinyint(1) DEFAULT 0 COMMENT '是否系统配置',
    `create_by` varchar(100) COMMENT '创建人',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` varchar(100) COMMENT '修改人',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统参数配置表';

-- ========================================
-- 11. 初始化数据
-- ========================================

-- 插入国家数据
INSERT INTO `transport_countries` (`code`, `name`, `create_by`) VALUES
('CN', '中国', 'admin');

-- 插入省份数据
INSERT INTO `transport_provinces` (`code`, `name`, `country_code`, `create_by`) VALUES
('110000', '北京市', 'CN', 'admin'),
('120000', '天津市', 'CN', 'admin'),
('130000', '河北省', 'CN', 'admin'),
('140000', '山西省', 'CN', 'admin'),
('150000', '内蒙古自治区', 'CN', 'admin'),
('210000', '辽宁省', 'CN', 'admin'),
('220000', '吉林省', 'CN', 'admin'),
('230000', '黑龙江省', 'CN', 'admin'),
('310000', '上海市', 'CN', 'admin'),
('320000', '江苏省', 'CN', 'admin'),
('330000', '浙江省', 'CN', 'admin'),
('340000', '安徽省', 'CN', 'admin'),
('350000', '福建省', 'CN', 'admin'),
('360000', '江西省', 'CN', 'admin'),
('370000', '山东省', 'CN', 'admin'),
('410000', '河南省', 'CN', 'admin'),
('420000', '湖北省', 'CN', 'admin'),
('430000', '湖南省', 'CN', 'admin'),
('440000', '广东省', 'CN', 'admin'),
('450000', '广西壮族自治区', 'CN', 'admin'),
('460000', '海南省', 'CN', 'admin'),
('500000', '重庆市', 'CN', 'admin'),
('510000', '四川省', 'CN', 'admin'),
('520000', '贵州省', 'CN', 'admin'),
('530000', '云南省', 'CN', 'admin'),
('540000', '西藏自治区', 'CN', 'admin'),
('610000', '陕西省', 'CN', 'admin'),
('620000', '甘肃省', 'CN', 'admin'),
('630000', '青海省', 'CN', 'admin'),
('640000', '宁夏回族自治区', 'CN', 'admin'),
('650000', '新疆维吾尔自治区', 'CN', 'admin');

-- 插入主要城市数据
INSERT INTO `transport_cities` (`code`, `name`, `province_code`, `create_by`) VALUES
-- 北京市
('110100', '北京市', '110000', 'admin'),
-- 上海市
('310100', '上海市', '310000', 'admin'),
-- 广东省主要城市
('440100', '广州市', '440000', 'admin'),
('440300', '深圳市', '440000', 'admin'),
('440400', '珠海市', '440000', 'admin'),
('440600', '佛山市', '440000', 'admin'),
('440700', '江门市', '440000', 'admin'),
('441300', '惠州市', '440000', 'admin'),
('441900', '东莞市', '440000', 'admin'),
('442000', '中山市', '440000', 'admin'),
-- 江苏省主要城市
('320100', '南京市', '320000', 'admin'),
('320200', '无锡市', '320000', 'admin'),
('320300', '徐州市', '320000', 'admin'),
('320500', '苏州市', '320000', 'admin'),
-- 浙江省主要城市
('330100', '杭州市', '330000', 'admin'),
('330200', '宁波市', '330000', 'admin'),
('330300', '温州市', '330000', 'admin'),
-- 山东省主要城市
('370100', '济南市', '370000', 'admin'),
('370200', '青岛市', '370000', 'admin'),
('370300', '淄博市', '370000', 'admin');

-- 插入主要区县数据
INSERT INTO `transport_districts` (`code`, `name`, `city_code`, `create_by`) VALUES
-- 北京市区县
('110101', '东城区', '110100', 'admin'),
('110102', '西城区', '110100', 'admin'),
('110105', '朝阳区', '110100', 'admin'),
('110106', '丰台区', '110100', 'admin'),
('110107', '石景山区', '110100', 'admin'),
('110108', '海淀区', '110100', 'admin'),
('110112', '通州区', '110100', 'admin'),
('110113', '顺义区', '110100', 'admin'),
('110114', '昌平区', '110100', 'admin'),
('110115', '大兴区', '110100', 'admin'),
-- 上海市区县
('310101', '黄浦区', '310100', 'admin'),
('310104', '徐汇区', '310100', 'admin'),
('310105', '长宁区', '310100', 'admin'),
('310106', '静安区', '310100', 'admin'),
('310107', '普陀区', '310100', 'admin'),
('310109', '虹口区', '310100', 'admin'),
('310110', '杨浦区', '310100', 'admin'),
('310112', '闵行区', '310100', 'admin'),
('310113', '宝山区', '310100', 'admin'),
('310114', '嘉定区', '310100', 'admin'),
-- 广州市区县
('440103', '荔湾区', '440100', 'admin'),
('440104', '越秀区', '440100', 'admin'),
('440105', '海珠区', '440100', 'admin'),
('440106', '天河区', '440100', 'admin'),
('440111', '白云区', '440100', 'admin'),
('440112', '黄埔区', '440100', 'admin'),
('440113', '番禺区', '440100', 'admin'),
('440114', '花都区', '440100', 'admin'),
('440115', '南沙区', '440100', 'admin');

-- 插入系统配置数据
INSERT INTO `transport_system_config` (`config_key`, `config_value`, `config_desc`, `config_type`, `is_system`, `create_by`) VALUES
('system.order.no.prefix', 'YS', '运输单号前缀', 'system', 1, 'admin'),
('system.order.no.length', '8', '运输单号长度(不含前缀)', 'system', 1, 'admin'),
('system.default.pricing.type', '1', '默认计费方式:1-按距离', 'business', 0, 'admin'),
('system.max.transport.distance', '2000', '最大运输距离(公里)', 'business', 0, 'admin'),
('system.min.transport.quantity', '0.1', '最小运输数量(吨)', 'business', 0, 'admin'),
('system.max.transport.quantity', '50', '最大运输数量(吨)', 'business', 0, 'admin'),
('system.order.auto.assign', 'false', '是否自动指派车辆司机', 'business', 0, 'admin'),
('system.notification.enabled', 'true', '是否启用消息通知', 'system', 0, 'admin'),
('system.backup.enabled', 'true', '是否启用数据备份', 'system', 0, 'admin'),
('system.log.retention.days', '90', '日志保留天数', 'system', 0, 'admin');

-- 插入基础商品数据
INSERT INTO `transport_product` (`product_name`, `product_code`, `product_type`, `product_spec`, `unit`, `density`, `product_status`, `create_by`) VALUES
('92号汽油', 'QY92', '汽油', '92#', '升', 0.725, 1, 'admin'),
('95号汽油', 'QY95', '汽油', '95#', '升', 0.737, 1, 'admin'),
('98号汽油', 'QY98', '汽油', '98#', '升', 0.740, 1, 'admin'),
('0号柴油', 'CY0', '柴油', '0#', '升', 0.835, 1, 'admin'),
('-10号柴油', 'CY-10', '柴油', '-10#', '升', 0.840, 1, 'admin'),
('-20号柴油', 'CY-20', '柴油', '-20#', '升', 0.845, 1, 'admin'),
('航空煤油', 'HKMJ', '航空燃料', 'RP-3', '升', 0.775, 1, 'admin'),
('润滑油', 'RHY', '润滑油', '通用型', '升', 0.900, 1, 'admin');

-- 插入基础运费规则数据
INSERT INTO `transport_pricing_rule` (`rule_name`, `pricing_type`, `base_price`, `unit_price`, `min_price`, `max_price`, `effective_date`, `priority`, `is_active`, `create_by`) VALUES
('通用按距离计费', 1, 500.00, 2.50, 300.00, 5000.00, '2024-01-01', 1, 1, 'admin'),
('通用按重量计费', 2, 300.00, 150.00, 200.00, 8000.00, '2024-01-01', 1, 1, 'admin'),
('通用按体积计费', 3, 400.00, 0.08, 250.00, 6000.00, '2024-01-01', 1, 1, 'admin'),
('短途固定价格', 4, 800.00, 0.00, 800.00, 800.00, '2024-01-01', 2, 1, 'admin');

-- 插入附加费用规则数据
INSERT INTO `transport_additional_fee` (`fee_name`, `fee_type`, `calculation_method`, `fee_amount`, `condition_desc`, `is_active`, `create_by`) VALUES
('过夜费', '过夜费', 1, 200.00, '运输时间超过24小时', 1, 'admin'),
('高速费', '高速费', 3, 0.50, '按实际高速公路里程计算', 1, 'admin'),
('偏远地区费', '偏远地区费', 2, 0.15, '偏远地区按运费15%收取', 1, 'admin'),
('装卸费', '装卸费', 4, 5.00, '按重量每吨收取装卸费', 1, 'admin'),
('等待费', '等待费', 1, 50.00, '等待时间超过2小时按小时收取', 1, 'admin');

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- ========================================
-- 建表完成提示
-- ========================================
SELECT '运输管理系统数据库表创建完成！' as message;
